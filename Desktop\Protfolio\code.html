<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Software Developer Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            scroll-behavior: smooth;
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .project-card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .skill-badge {
            transition: all 0.2s ease;
        }
        
        .skill-badge:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm fixed w-full z-10">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-semibold text-gray-800">Saad Meloud</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-600 hover:text-indigo-600">Home</a>
                    <a href="#about" class="text-gray-600 hover:text-indigo-600">About</a>
                    <a href="#projects" class="text-gray-600 hover:text-indigo-600">Projects</a>
                    <a href="#contact" class="text-gray-600 hover:text-indigo-600">Contact</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button class="mobile-menu-button">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div class="mobile-menu hidden md:hidden bg-white shadow-lg">
            <a href="#home" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Home</a>
            <a href="#about" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">About</a>
            <a href="#projects" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Projects</a>
            <a href="#contact" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Contact</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-gradient pt-24 pb-20">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">Hi, I'm Saad Meloud</h1>
                    <h2 class="text-2xl md:text-3xl font-semibold text-indigo-600 mb-6">Software Developer</h2>
                    <p class="text-gray-600 text-lg mb-8">
                        With 2.5 years of experience developing web and mobile applications. 
                        I've completed around 30 projects using Django, React, and Flutter.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#projects" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                            View My Work
                        </a>
                        <a href="#contact" class="border border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-6 py-3 rounded-lg font-medium transition duration-300">
                            Contact Me
                        </a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-white shadow-xl">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/f33a66d8-f12a-4338-8adb-41b0e2ccc689.png" alt="Portrait of Saad Meloud, a professional software developer with short hair and glasses, wearing a casual shirt" class="w-full h-full object-cover" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">About Me</h2>
            
            <div class="md:flex items-center">
                <div class="md:w-1/3 mb-10 md:mb-0 flex justify-center">
                    <div class="w-64 h-64 rounded-lg overflow-hidden shadow-lg">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/8d8dca75-d754-47e8-b572-cdd031fa8213.png" alt="Saad Meloud working at his computer in a modern office environment with multiple monitors showing code" class="w-full h-full object-cover" />
                    </div>
                </div>
                <div class="md:w-2/3 md:pl-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-4">Professional Experience</h3>
                    <p class="text-gray-600 mb-6">
                        I'm a passionate software developer with 2.5 years of hands-on experience in building web and mobile applications. 
                        Throughout my career, I've successfully delivered around 30 projects, ranging from small business websites 
                        to complex web applications and mobile apps.
                    </p>
                    
                    <h3 class="text-2xl font-semibold text-gray-800 mb-4">Technical Skills</h3>
                    <div class="flex flex-wrap gap-3 mb-8">
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Django</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">React</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Flutter</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Python</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">JavaScript</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">HTML/CSS</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">REST APIs</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Git</span>
                    </div>
                    
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">Development Approach</h4>
                        <p class="text-gray-600">
                            I focus on creating clean, efficient, and maintainable code. My expertise lies in full-stack web development with Django and React, 
                            as well as cross-platform mobile development with Flutter. I'm passionate about continuous learning and staying updated with 
                            the latest technologies and best practices in software development.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">My Projects</h2>
            <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto">
                Here are some of my notable projects. I've completed around 30 projects in total, specializing in web and mobile development.
            </p>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/3566ad45-1635-472b-9cbe-4e90c87a2128.png" alt="Web application dashboard screenshot showing analytics charts and data tables built with Django and React" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">E-Commerce Platform</h3>
                        <p class="text-gray-600 mb-4">
                            A full-featured e-commerce platform built with Django backend and React frontend, supporting product listings, cart functionality, and payment processing.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">React</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">REST API</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/e916b3fc-cfab-42f1-aaca-44ce2a665f63.png" alt="Mobile app screenshots showing task management interface on both iOS and Android devices built with Flutter" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Task Management App</h3>
                        <p class="text-gray-600 mb-4">
                            Cross-platform mobile application developed with Flutter for organizing tasks with categories, priorities, and deadlines.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Flutter</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Firebase</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Dart</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 3 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/66c4c0ec-2306-40c5-a757-21cbcb951cb4.png" alt="CMS interface showing blog post management screens with preview functionality built with Django" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Content Management System</h3>
                        <p class="text-gray-600 mb-4">
                            Custom CMS built with Django allowing users to create, edit, and publish content with rich text editing and media management.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">PostgreSQL</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Bootstrap</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 4 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/1467d662-8d49-42d4-abaa-b99a72a8df50.png" alt="Mobile app screens showing weather forecast with animated icons and location-based data built with Flutter" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Weather Forecast App</h3>
                        <p class="text-gray-600 mb-4">
                            Location-based weather application providing detailed forecasts with beautiful animations and intuitive UI.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Flutter</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">OpenWeather API</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Geolocation</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 5 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/2a6ef363-6331-4e50-8158-b7d946e66fb3.png" alt="Social media platform interface showing user profiles and activity feed built with React and Django REST Framework" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Social Media Platform</h3>
                        <p class="text-gray-600 mb-4">
                            A community platform with user profiles, posts, comments, and real-time notifications.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django REST</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">React</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">WebSockets</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 6 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/b04e602d-1fb6-4399-a188-5c71ea651fc1.png" alt="Inventory management system dashboard showing product stock levels and analytics charts" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Inventory System</h3>
                        <p class="text-gray-600 mb-4">
                            Comprehensive inventory management solution with barcode scanning, reporting, and analytics.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">JavaScript</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Barcode API</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="https://github.com/saadmeloude" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    View More on GitHub
                    <svg class="ml-3 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Get In Touch</h2>
            
            <div class="md:flex">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="bg-gray-50 p-8 rounded-lg shadow-sm h-full">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Contact Information</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">Phone</p>
                                    <p class="text-gray-800 font-medium">+216 32 816 779</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">Email</p>
                                    <p class="text-gray-800 font-medium"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">GitHub</p>
                                    <a href="https://github.com/saadmeloude" class="text-indigo-600 hover:text-indigo-800 font-medium">github.com/saadmeloude</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="md:w-1/2 md:pl-10">
                    <form class="bg-gray-50 p-8 rounded-lg shadow-sm">
                        <div class="mb-6">
                            <label for="name" class="block text-gray-700 font-medium mb-2">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Your name">
                        </div>
                        
                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                            <textarea id="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Your message..."></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-300">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex md:items-center md:justify-between">
                <div class="text-center md:text-left mb-6 md:mb-0">
                    <h3 class="text-xl font-semibold">Saad Meloud</h3>
                    <p class="text-gray-400 mt-2">Software Developer specialized in web and mobile applications</p>
                </div>
                
                <div class="flex justify-center md:justify-end space-x-6">
                    <a href="https://github.com/saadmeloude" class="text-gray-400 hover:text-white">
                        <span class="sr-only">GitHub</span>
                        <i class="fab fa-github fa-lg"></i>
                    </a>
                    <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Email</span>
                        <i class="fas fa-envelope fa-lg"></i>
                    </a>
                    <a href="tel:+21632816779" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Phone</span>
                        <i class="fas fa-phone fa-lg"></i>
                    </a>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400 text-sm">
                <p>© 2023 Saad Meloud. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 100,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    mobileMenu.classList.add('hidden');
                }
            });
        });
        
        // Form submission
        const contactForm = document.querySelector('form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form values
                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                const message = document.getElementById('message').value;
                
                // In a real scenario, you would send this data to a server
                console.log('Form submitted:', { name, email, message });
                
                // Show a success message
                alert('Thank you for your message! I will get back to you soon.');
                
                // Reset the form
                contactForm.reset();
            });
        }
    </script>
</body>
</html>


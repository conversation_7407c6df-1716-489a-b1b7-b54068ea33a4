<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Portfolio Développeur Logiciel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.emailjs.com/dist/email.min.js"></script>
    <script src="email-config.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            scroll-behavior: smooth;
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .project-card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .skill-badge {
            transition: all 0.2s ease;
        }
        
        .skill-badge:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm fixed w-full z-10">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-semibold text-gray-800">Saad Meiloud</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-600 hover:text-indigo-600">Accueil</a>
                    <a href="#about" class="text-gray-600 hover:text-indigo-600">À propos</a>
                    <a href="#projects" class="text-gray-600 hover:text-indigo-600">Projets</a>
                    <a href="#contact" class="text-gray-600 hover:text-indigo-600">Contact</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button class="mobile-menu-button">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div class="mobile-menu hidden md:hidden bg-white shadow-lg">
            <a href="#home" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Accueil</a>
            <a href="#about" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">À propos</a>
            <a href="#projects" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Projets</a>
            <a href="#contact" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Contact</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-gradient pt-24 pb-20">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">Salut, je suis Saad Meiloud</h1>
                    <h2 class="text-2xl md:text-3xl font-semibold text-indigo-600 mb-6">Développeur Logiciel</h2>
                    <p class="text-gray-600 text-lg mb-8">
                        Avec 2,5 ans d'expérience dans le développement d'applications web et mobiles.
                        J'ai réalisé environ 30 projets en utilisant Django, React et Flutter.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#projects" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                            Voir mes projets
                        </a>
                        <a href="#contact" class="border border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-6 py-3 rounded-lg font-medium transition duration-300">
                            Me contacter
                        </a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-white shadow-xl">
                        <img src="saad-photo.jpg" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">À propos de moi</h2>

            <div class="md:flex items-center">
                <div class="md:w-1/3 mb-10 md:mb-0 flex justify-center">
                    <div class="w-64 h-64 rounded-lg overflow-hidden shadow-lg">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/8d8dca75-d754-47e8-b572-cdd031fa8213.png" alt="Saad Meloud travaillant sur son ordinateur dans un environnement de bureau moderne avec plusieurs moniteurs affichant du code" class="w-full h-full object-cover" />
                    </div>
                </div>
                <div class="md:w-2/3 md:pl-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-4">Expérience Professionnelle</h3>
                    <p class="text-gray-600 mb-6">
                        Je suis un développeur logiciel passionné avec 2,5 ans d'expérience pratique dans la création d'applications web et mobiles.
                        Tout au long de ma carrière, j'ai livré avec succès environ 30 projets, allant de petits sites web d'entreprise
                        à des applications web complexes et des applications mobiles.
                    </p>

                    <h3 class="text-2xl font-semibold text-gray-800 mb-4">Compétences Techniques</h3>
                    <div class="flex flex-wrap gap-3 mb-8">
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Django</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">React</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Flutter</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Python</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">JavaScript</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">HTML/CSS</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">REST APIs</span>
                        <span class="skill-badge bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">Git</span>
                    </div>
                    
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">Approche de Développement</h4>
                        <p class="text-gray-600">
                            Je me concentre sur la création de code propre, efficace et maintenable. Mon expertise réside dans le développement web full-stack avec Django et React,
                            ainsi que dans le développement mobile multiplateforme avec Flutter. Je suis passionné par l'apprentissage continu et je reste à jour avec
                            les dernières technologies et les meilleures pratiques en développement logiciel.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Mes Projets</h2>
            <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto">
                Voici quelques-uns de mes projets notables. J'ai réalisé environ 30 projets au total, spécialisé dans le développement web et mobile.
            </p>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/3566ad45-1635-472b-9cbe-4e90c87a2128.png" alt="Web application dashboard screenshot showing analytics charts and data tables built with Django and React" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Plateforme E-Commerce</h3>
                        <p class="text-gray-600 mb-4">
                            Une plateforme e-commerce complète construite avec Django en backend et React en frontend, supportant les listes de produits, la fonctionnalité panier et le traitement des paiements.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">React</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">REST API</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/e916b3fc-cfab-42f1-aaca-44ce2a665f63.png" alt="Mobile app screenshots showing task management interface on both iOS and Android devices built with Flutter" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Task Management App</h3>
                        <p class="text-gray-600 mb-4">
                            Cross-platform mobile application developed with Flutter for organizing tasks with categories, priorities, and deadlines.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Flutter</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Firebase</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Dart</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 3 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/66c4c0ec-2306-40c5-a757-21cbcb951cb4.png" alt="CMS interface showing blog post management screens with preview functionality built with Django" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Content Management System</h3>
                        <p class="text-gray-600 mb-4">
                            Custom CMS built with Django allowing users to create, edit, and publish content with rich text editing and media management.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">PostgreSQL</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Bootstrap</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 4 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/1467d662-8d49-42d4-abaa-b99a72a8df50.png" alt="Mobile app screens showing weather forecast with animated icons and location-based data built with Flutter" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Weather Forecast App</h3>
                        <p class="text-gray-600 mb-4">
                            Location-based weather application providing detailed forecasts with beautiful animations and intuitive UI.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Flutter</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">OpenWeather API</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Geolocation</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 5 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/2a6ef363-6331-4e50-8158-b7d946e66fb3.png" alt="Social media platform interface showing user profiles and activity feed built with React and Django REST Framework" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Social Media Platform</h3>
                        <p class="text-gray-600 mb-4">
                            A community platform with user profiles, posts, comments, and real-time notifications.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django REST</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">React</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">WebSockets</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 6 -->
                <div class="project-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-48 overflow-hidden">
                        <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/b04e602d-1fb6-4399-a188-5c71ea651fc1.png" alt="Inventory management system dashboard showing product stock levels and analytics charts" class="w-full h-full object-cover" />
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Inventory System</h3>
                        <p class="text-gray-600 mb-4">
                            Comprehensive inventory management solution with barcode scanning, reporting, and analytics.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Django</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">JavaScript</span>
                            <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Barcode API</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="https://github.com/saadmeloude" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    View More on GitHub
                    <svg class="ml-3 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Contactez-moi</h2>
            
            <div class="md:flex">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="bg-gray-50 p-8 rounded-lg shadow-sm h-full">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Informations de Contact</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">Téléphone</p>
                                    <a href="tel:+22232816779" class="text-gray-800 font-medium hover:text-indigo-600">+222 32816779</a>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">Email</p>
                                    <a href="mailto:<EMAIL>" class="text-gray-800 font-medium hover:text-indigo-600"><EMAIL></a>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <i class="fab fa-whatsapp text-green-600 text-xl"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">WhatsApp</p>
                                    <a href="https://wa.me/22232816779" target="_blank" class="text-gray-800 font-medium hover:text-green-600">+222 32816779</a>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-1">
                                    <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-500">GitHub</p>
                                    <a href="https://github.com/saadmeiloude" class="text-indigo-600 hover:text-indigo-800 font-medium">github.com/saadmeiloude</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="md:w-1/2 md:pl-10">
                    <!-- Web3Forms - أسهل حل لإرسال الرسائل -->
                    <form id="contact-form" action="https://api.web3forms.com/submit" method="POST" class="bg-gray-50 p-8 rounded-lg shadow-sm">
                        <!-- Web3Forms Access Key - احصل على مفتاح مجاني من https://web3forms.com -->
                        <input type="hidden" name="access_key" value="YOUR_ACCESS_KEY_HERE">
                        <input type="hidden" name="subject" value="Contact depuis le portfolio">
                        <input type="hidden" name="from_name" value="Portfolio Contact Form">

                        <div class="mb-6">
                            <label for="name" class="block text-gray-700 font-medium mb-2">Nom</label>
                            <input type="text" id="name" name="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Votre nom" required>
                        </div>

                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email</label>
                            <input type="email" id="email" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="<EMAIL>" required>
                        </div>

                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Votre message..." required></textarea>
                        </div>

                        <!-- Honeypot for spam protection -->
                        <input type="checkbox" name="botcheck" class="hidden" style="display: none;">

                        <div class="flex space-x-4">
                            <button type="submit" id="submit-btn" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-300">
                                📧 Envoyer par Email
                            </button>
                            <button type="button" id="whatsapp-btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-300">
                                📱 WhatsApp
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex md:items-center md:justify-between">
                <div class="text-center md:text-left mb-6 md:mb-0">
                    <h3 class="text-xl font-semibold">Saad Meiloud</h3>
                    <p class="text-gray-400 mt-2">Développeur Logiciel spécialisé dans les applications web et mobiles</p>
                </div>
                
                <div class="flex justify-center md:justify-end space-x-6">
                    <a href="https://github.com/saadmeiloude" class="text-gray-400 hover:text-white">
                        <span class="sr-only">GitHub</span>
                        <i class="fab fa-github fa-lg"></i>
                    </a>
                    <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Email</span>
                        <i class="fas fa-envelope fa-lg"></i>
                    </a>
                    <a href="https://wa.me/22232816779" target="_blank" class="text-gray-400 hover:text-green-400">
                        <span class="sr-only">WhatsApp</span>
                        <i class="fab fa-whatsapp fa-lg"></i>
                    </a>
                    <a href="tel:+22232816779" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Phone</span>
                        <i class="fas fa-phone fa-lg"></i>
                    </a>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400 text-sm">
                <p>© 2023 Saad Meiloud. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 100,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    mobileMenu.classList.add('hidden');
                }
            });
        });
        
        // Form submission with direct email client
        const contactForm = document.getElementById('contact-form');
        const submitBtn = document.getElementById('submit-btn');
        const whatsappBtn = document.getElementById('whatsapp-btn');

        // Function to get form data
        function getFormData() {
            return {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                message: document.getElementById('message').value
            };
        }

        // Function to validate form
        function validateForm(formData) {
            if (!formData.name || !formData.email || !formData.message) {
                alert('Veuillez remplir tous les champs.');
                return false;
            }
            return true;
        }

        // Email submission with Web3Forms
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = getFormData();
                if (!validateForm(formData)) return;

                // Change button text to show loading
                const originalText = submitBtn.textContent;
                submitBtn.textContent = '📧 Envoi en cours...';
                submitBtn.disabled = true;

                // Check if access key is configured
                const accessKey = contactForm.querySelector('input[name="access_key"]').value;

                if (accessKey && accessKey !== 'YOUR_ACCESS_KEY_HERE') {
                    // Send via Web3Forms
                    const formDataObj = new FormData(contactForm);

                    fetch('https://api.web3forms.com/submit', {
                        method: 'POST',
                        body: formDataObj
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log('Email sent successfully via Web3Forms:', data);
                            alert('✅ Votre message a été envoyé avec succès ! Merci pour votre contact.');
                            contactForm.reset();
                        } else {
                            throw new Error(data.message || 'Erreur lors de l\'envoi');
                        }
                    })
                    .catch(error => {
                        console.error('Web3Forms error:', error);
                        // Fallback to mailto
                        fallbackToMailto(formData);
                    })
                    .finally(() => {
                        // Reset button
                        submitBtn.textContent = originalText;
                        submitBtn.disabled = false;
                    });
                } else {
                    // Access key not configured, use mailto fallback
                    console.log('Web3Forms access key not configured, using mailto fallback');
                    fallbackToMailto(formData);
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        // Fallback function for mailto
        function fallbackToMailto(formData) {
            const subject = encodeURIComponent('Contact depuis le portfolio - ' + formData.name);
            const body = encodeURIComponent(
                `Bonjour Saad,\n\n` +
                `Vous avez reçu un nouveau message depuis votre portfolio :\n\n` +
                `Nom: ${formData.name}\n` +
                `Email: ${formData.email}\n\n` +
                `Message:\n${formData.message}\n\n` +
                `Cordialement,\n${formData.name}`
            );

            const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
            window.open(mailtoLink, '_blank');

            alert('📧 Votre client email va s\'ouvrir avec le message pré-rempli. Merci pour votre contact !');
            contactForm.reset();
        }

        // WhatsApp submission
        if (whatsappBtn) {
            whatsappBtn.addEventListener('click', function() {
                const formData = getFormData();
                if (!validateForm(formData)) return;

                // Create WhatsApp message
                const whatsappMessage = encodeURIComponent(
                    `Bonjour Saad ! 👋\n\n` +
                    `Je vous contacte depuis votre portfolio :\n\n` +
                    `🧑‍💼 Nom: ${formData.name}\n` +
                    `📧 Email: ${formData.email}\n\n` +
                    `💬 Message:\n${formData.message}\n\n` +
                    `Merci !`
                );

                // Open WhatsApp with pre-filled message
                const whatsappLink = `https://wa.me/22232816779?text=${whatsappMessage}`;
                window.open(whatsappLink, '_blank');

                // Show success message
                alert('WhatsApp va s\'ouvrir avec votre message pré-rempli. Merci pour votre contact !');

                // Reset form
                contactForm.reset();
            });
        }
    </script>
</body>
</html>

